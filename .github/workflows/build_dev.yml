name: Build DEV CI

on:
  push:
    branches: ["dev"]
    paths:
      - ".github/workflows/build_dev.yml"
      - "kernel/**"
      - "user/**"
      - "tools/**"
      - "version"
  pull_request:
    branches: ["dev"]
    paths:
      - ".github/workflows/build_dev.yml"
      - "kernel/**"
      - "user/**"
      - "tools/**"
      - "version"
  workflow_call:
  workflow_dispatch:

jobs:
  Build-kpimg:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Check out
        uses: actions/checkout@v3
        with:
          submodules: "recursive"
          fetch-depth: 0
      - name: Generate version
        id: parse_version
        run: |
          MAJOR=$(grep '#define MAJOR' version | awk '{print $3}')
          MINOR=$(grep '#define MINOR' version | awk '{print $3}')
          PATCH=$(grep '#define PATCH' version | awk '{print $3}')
          VERSION="$MAJOR.$MINOR.$PATCH"
          echo "Generated Version: $VERSION"
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
      - name: Install Compiler
        run: |
          curl -o arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-elf.tar.xz https://armkeil.blob.core.windows.net/developer/Files/downloads/gnu/12.2.rel1/binrel/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-elf.tar.xz
          tar -Jxf arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-elf.tar.xz
      - name: Build kpimg
        run: |
          export TARGET_COMPILE=`pwd`/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-elf/bin/aarch64-none-elf-
          cd kernel

          export ANDROID=1
          make
          mv kpimg kpimg-android
          mv kpimg.elf kpimg.elf-android
          make clean

          unset ANDROID
          make
          mv kpimg kpimg-linux
          mv kpimg.elf kpimg.elf-linux
          make clean

          cd ..
          cd kpms

          cd demo-hello
          make
          mv hello.kpm demo-hello.kpm

          cd ../demo-inlinehook
          make
          mv inlinehook.kpm demo-inlinehook.kpm

          cd ../demo-syscallhook
          make
          mv syscallhook.kpm demo-syscallhook.kpm

      - name: Upload elf
        uses: actions/upload-artifact@v3
        with:
          path: |
            kernel/kpimg.elf-linux
            kernel/kpimg.elf-android
          name: kpimg.elf

      - name: Release
        uses: ncipollo/release-action@v1.12.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: ${{ steps.parse_version.outputs.VERSION }}-dev
          artifacts: |
            kernel/kpimg-linux
            kernel/kpimg-android
            kpms/demo-hello/demo-hello.kpm
            kpms/demo-inlinehook/demo-inlinehook.kpm
            kpms/demo-syscallhook/demo-syscallhook.kpm
          generateReleaseNotes: true
          allowUpdates: true
          replacesArtifacts: true
          prerelease: true

  Release-user-lib:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Check out
        uses: actions/checkout@v3
      - name: Generate version
        id: parse_version
        run: |
          MAJOR=$(grep '#define MAJOR' version | awk '{print $3}')
          MINOR=$(grep '#define MINOR' version | awk '{print $3}')
          PATCH=$(grep '#define PATCH' version | awk '{print $3}')
          VERSION="$MAJOR.$MINOR.$PATCH"
          echo "Generated Version: $VERSION"
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

      - name: Zip-lib
        run: |
          export TARGET_COMPILE=placeholder
          cd kernel
          make hdr
          cd ..
          zip -r kpuser.zip user

      - name: Release
        uses: ncipollo/release-action@v1.12.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: ${{ steps.parse_version.outputs.VERSION }}-dev
          artifacts: |
            kpuser.zip
          allowUpdates: true
          replacesArtifacts: true
          prerelease: true

  Build-android-kptools:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Check out
        uses: actions/checkout@v3
        with:
          submodules: "recursive"
          fetch-depth: 0
      - name: Generate version
        id: parse_version
        run: |
          MAJOR=$(grep '#define MAJOR' version | awk '{print $3}')
          MINOR=$(grep '#define MINOR' version | awk '{print $3}')
          PATCH=$(grep '#define PATCH' version | awk '{print $3}')
          VERSION="$MAJOR.$MINOR.$PATCH"
          echo "Generated Version: $VERSION"
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

      - name: Setup Android NDK
        uses: nttld/setup-ndk@v1
        id: setup-ndk
        with:
          ndk-version: r26b
          add-to-path: true

      - name: Make hdr
        run: |
          export TARGET_COMPILE=placeholder
          cd kernel
          make hdr

      - name: Build kptools-android
        run: |
          cd tools
          mkdir -p build/android && cd build/android
          echo ${{ steps.setup-ndk.outputs.ndk-path }}
          cmake \
            -DCMAKE_TOOLCHAIN_FILE=${{ steps.setup-ndk.outputs.ndk-path }}/build/cmake/android.toolchain.cmake \
            -DCMAKE_BUILD_TYPE=Release \
            -DANDROID_PLATFORM=android-33 \
            -DANDROID_ABI=arm64-v8a ../..
          cmake --build .
          mv kptools kptools-android

      - name: Release
        uses: ncipollo/release-action@v1.12.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: ${{ steps.parse_version.outputs.VERSION }}-dev
          artifacts: |
            tools/build/android/kptools-android
          allowUpdates: true
          replacesArtifacts: true
          prerelease: true

  Build-kptools-linux:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Check out
        uses: actions/checkout@v3
      - name: Generate version
        id: parse_version
        run: |
          MAJOR=$(grep '#define MAJOR' version | awk '{print $3}')
          MINOR=$(grep '#define MINOR' version | awk '{print $3}')
          PATCH=$(grep '#define PATCH' version | awk '{print $3}')
          VERSION="$MAJOR.$MINOR.$PATCH"
          echo "Generated Version: $VERSION"
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

      - name: Make hdr
        run: |
          export TARGET_COMPILE=placeholder
          cd kernel
          make hdr

      - name: Build kptools
        run: |
          export ANDROID=1
          cd tools
          mkdir -p build && cd build
          cmake ..
          make
          mv kptools kptools-linux

      - name: Release
        uses: ncipollo/release-action@v1.12.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: ${{ steps.parse_version.outputs.VERSION }}-dev
          artifacts: |
            tools/build/kptools-linux
          allowUpdates: true
          replacesArtifacts: true
          prerelease: true

  Build-kptools-mac:
    runs-on: macos-latest
    permissions:
      contents: write
    steps:
      - name: Check out
        uses: actions/checkout@v3
      - name: Generate version
        id: parse_version
        run: |
          MAJOR=$(grep '#define MAJOR' version | awk '{print $3}')
          MINOR=$(grep '#define MINOR' version | awk '{print $3}')
          PATCH=$(grep '#define PATCH' version | awk '{print $3}')
          VERSION="$MAJOR.$MINOR.$PATCH"
          echo "Generated Version: $VERSION"
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

      - name: Make hdr
        run: |
          export TARGET_COMPILE=placeholder
          cd kernel
          make hdr

      - name: Build kptools
        run: |
          export ANDROID=1
          cd tools
          mkdir -p build && cd build
          cmake ..
          make
          mv kptools kptools-mac
      - name: Release
        uses: ncipollo/release-action@v1.12.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: ${{ steps.parse_version.outputs.VERSION }}-dev
          artifacts: |
            tools/build/kptools-mac
          allowUpdates: true
          replacesArtifacts: true
          prerelease: true
